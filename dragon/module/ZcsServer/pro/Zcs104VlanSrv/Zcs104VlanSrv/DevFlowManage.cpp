#include "DevFlowManage.h"

DevFlowManage::DevFlowManage() :m_pFrontPtr(NULL),m_pStnPtr(NULL),m_pLogFile(NULL),
        m_lockDevMap("m_lockDevMap")
{
    m_sStn.clear();
    
    m_hLinkThdId = 0;
    m_linkThdHandle = 0;
    
    m_hDealForDatId = 0 ;
    m_dealForDatHandle = 0;
    
    m_pSend2FrontFun    = NULL;       // 向前置发送数据 回调
    m_pFntDevStaChanFun = NULL;       // 前置设备连接状态通道回调
    m_pSend2StnFun      = NULL;       // 向子站发送数据回调
    m_pSendConn2StnFun = NULL;        // 向子站发送y设备连接重连
    m_pSendConnClose2StnFun = NULL;   // 向子站发送关闭设备连接
    
    m_nMaxTimeOut == 0 ? 30 : m_nMaxTimeOut; // 秒
    m_bExit = false;
}

DevFlowManage::~DevFlowManage() 
{
    //reset();
}

void DevFlowManage::_init(CMessageLog  *pLogFile,bool *bExit,const std::string &sStn)
{
    if(NULL!= pLogFile) m_pLogFile = pLogFile;
    m_sStn = sStn;
    
    startStnLinkThread();
    startDatHandCenter();
    
    m_pLogFile->FormatAdd(CLogFile::trace, "DevFlowManage: [STN:%s] _init success", m_sStn.c_str());
}

 // maxTimeOut单位秒
void DevFlowManage::setMaxTimeOut(int nTimeOut)
{
    m_nMaxTimeOut = ( nTimeOut == 0 ? 30 : nTimeOut); // 秒
    m_pLogFile->FormatAdd(CLogFile::trace, "DevFlowManage: [STN:%s] MaxTimeOut %d ", m_sStn.c_str(),m_nMaxTimeOut);
}

void DevFlowManage::reset()
{
    CLockUp lockAuto(&m_lockDevMap);
    for(std::map<int,DeviceObj*>::iterator it  = m_mapDev.begin(); it!=m_mapDev.end(); it++)
    {
        DeviceObj *ptrObj = it->second;
        if(NULL!= ptrObj)
        {
            ptrObj->clearFntMsg();
            ptrObj->clearStnMsg();
            delete ptrObj;
        }
    }
    m_mapDev.clear();
   // m_pLogFile->FormatAdd(CLogFile::trace, "DevFlowManage: [STN:%s] reset ", m_sStn.c_str());
}

void DevFlowManage::release()
{
    m_bExit = true;
    endLinkThread();
    endDatHandCenter();
    reset();
    m_pLogFile = NULL;
}

 // 添加设备
void DevFlowManage::addSecDev(std::string &chNo,std::string &name, std::string &proType)
{
    if(chNo.empty()) return ;
    int nChNo = atoi(chNo.c_str());

    std::size_t pos = proType.find_first_of(':');
    if (std::string::npos == pos) {
        m_pLogFile->FormatAdd(CLogFile::trace, "DevFlowManage: [stn:%s] [chNo:%s] addSecDev(), ied_name: %s , p_type: %s , 规约类型获取失败", m_sStn.c_str(), chNo.c_str(), name.c_str(), proType.c_str());
        return; // 查找不到规约类型 返回
    }
    std::string sPType = proType.substr(0, pos);
    
    CLockUp lockAuto(&m_lockDevMap);
    std::map<int,DeviceObj*>::iterator it = m_mapDev.find(nChNo);
    if(it!= m_mapDev.end())
    {
        DeviceObj *ptrObj = it->second;
        if(NULL!= ptrObj)
        {
             ptrObj->clearFntMsg();
             ptrObj->clearStnMsg();
             m_mapDev.erase(it);
        }
    }
     
    DeviceObj *devObj = new DeviceObj(nChNo,name,atoi(sPType.c_str()));
    m_mapDev.insert(make_pair(nChNo,devObj));
    
   m_pLogFile->FormatAdd(CLogFile::trace,"DevFlowManage: [STN:%s] [chNo:%d]  添加站端设备,name:%s, protype:%s ",m_sStn.c_str(),nChNo,name.c_str(),proType.c_str());   
}

// 向站端缓存添加数据
bool DevFlowManage::addStnMsg(int nChNo,std::string &msg)
{
     CLockUp lockAuto(&m_lockDevMap);
     std::map<int,DeviceObj*>::iterator it = m_mapDev.find(nChNo);
     if(it!= m_mapDev.end())
     {
         DeviceObj *ptrObj = it->second;
         if(NULL!= ptrObj)
         {
              ptrObj->addStnMsg(msg);
              m_pLogFile->FormatAdd(CLogFile::trace, "DevFlowManage: [STN:%s]  [ChNo:%d] 向站端设备添加数据,长度: %d",m_sStn.c_str(),nChNo,msg.length());
              if(!ptrObj->getFntSta())
              {
                   ptrObj->setFntSta(true); // 向子站端添加数据时前置状态肯定为真
                   m_pLogFile->FormatAdd(CLogFile::trace, "DevFlowManage: [STN:%s]  [ChNo:%d] 改变当前前置连接为true: %d",m_sStn.c_str(),nChNo,msg.length());
              }
              return true;
         }
     }
    
    return false;
}

// 向前置缓存添加数据
bool DevFlowManage::addFntMsg(int nChNo,std::string msg)
{
     CLockUp lockAuto(&m_lockDevMap);
     std::map<int,DeviceObj*>::iterator it = m_mapDev.find(nChNo);
     if(it!= m_mapDev.end())
     {
         DeviceObj *ptrObj = it->second;
         if(NULL!= ptrObj)
         {
            ptrObj->addFntMsg(msg);
            m_pLogFile->FormatAdd(CLogFile::trace, "DevFlowManage: [STN:%s]  [ChNo:%d] 向前置设备添加数据: 长度 %d",m_sStn.c_str(),nChNo,msg.length());
            return true;
         }
     }
    
     return false;
}

// 设置站端设备连接状态
bool DevFlowManage::setDevStnSta(int nChNo,LinkStatus sta)
{
     CLockUp lockAuto(&m_lockDevMap);
     std::map<int,DeviceObj*>::iterator it = m_mapDev.find(nChNo);
     if(it!= m_mapDev.end())
     {
         DeviceObj *ptrObj = it->second;
         if(NULL!= ptrObj)
         {
             LinkStatus preSta = ptrObj->getStnSta();
             if(sta != preSta )
             {
                 ptrObj->setStnSta(sta);
                 
                 m_pLogFile->FormatAdd(CLogFile::trace, "DevFlowManage: [STN:%s] [ChNo:%d] 站端设备状态发生改变 ,sta %s ,改变前:%d, 改变后:%d ",
                         m_sStn.c_str(),nChNo,(sta == CONNECT_CLOSE) ? "连接关闭":"连接成功",static_cast<int>(preSta),static_cast<int>(sta));
                 
                 if(ptrObj->getFntSta())nofifyFrontDevState(nChNo,(ptrObj->getStnSta()==CONNECT_CLOSE) ? 0:1) ;  // 向前置发送设备连接状态通知(前置设备没连上来不发)
                 return true;
             }
         }
     }
     return false;
}

// 获取站端设备连接状态
 LinkStatus DevFlowManage::getDevStnSta(int nChNo)
 {
     CLockUp lockAuto(&m_lockDevMap);
     std::map<int,DeviceObj*>::iterator it = m_mapDev.find(nChNo);
     if(it!= m_mapDev.end())
     {
         DeviceObj *ptrObj = it->second;
         if(NULL!= ptrObj)
         {
             return ptrObj->getStnSta();
         }
     }
     
     return CONNECT_CLOSE;
 }

// 设置前置设备连接状态
bool DevFlowManage::setDevFntSta(int nChNo,bool bSta)
{
     CLockUp lockAuto(&m_lockDevMap);
     std::map<int,DeviceObj*>::iterator it = m_mapDev.find(nChNo);
     if(it!= m_mapDev.end())
     {
         DeviceObj *ptrObj = it->second;
         if(NULL!= ptrObj)
         {
             if(bSta!=ptrObj->getFntSta())  
             {
                  ptrObj->setFntSta(bSta);
                  m_pLogFile->FormatAdd(CLogFile::trace, "DevFlowManage: [STN:%s] [ChNo:%d] setDevFntSta 前置设备连接状态更新，当前状态 %d ",m_sStn.c_str(),nChNo,bSta? 1:0);          
             }
         }
     }
     return true;
}

bool DevFlowManage::isExit()
{
   return m_bExit;
}

 // 站端连接状态线程
bool DevFlowManage::startStnLinkThread()
{
    int nRet = 0;
    nRet = xj_thread_create(&m_linkThdHandle, &m_hLinkThdId, handleLinkThread, this);
    if (nRet != 0) {
        m_pLogFile->FormatAdd(CLogFile::trace, "DevFlowManage:[stn:%s]  创建 startHandleLinkeThread 线程失败",m_sStn.c_str());
        return false;
    }

     m_pLogFile->FormatAdd(CLogFile::trace, "DevFlowManage: [stn:%s]  创建 startHandleLinkeThread  线程成功",m_sStn.c_str());
    return true;
}

void DevFlowManage::endLinkThread()
{
     if (0 != m_hLinkThdId) {
        int nRet = xj_thread_join(m_hLinkThdId, NULL);
        if (nRet != 0) {
            //m_pLogFile->FormatAdd(CLogFile::trace, "DevFlowManage: [stn:%s] endLinkThread()退出线程异常",m_sStn.c_str());
            return;
        }
    }
   printf("DevFlowManage:退出 endLinkThread 线程成功");
}

THREAD_FUNC WINAPI DevFlowManage::handleLinkThread( LPVOID pParam )
{
    DevFlowManage* pThis = (DevFlowManage*) pParam;
    if(NULL!= pThis->m_pLogFile)pThis->m_pLogFile->FormatAdd(CLogFile::error,"DevFlowManage:  handleLinkThread() 线程ID %d ", syscall(SYS_gettid));
    while (!pThis->isExit()) {
        pThis->handLinkLoop();
        MySleep(1000 * 3);
    }
    printf("DevFlowManage:  退出 handleLinkThread() 线程成功");
    return THREAD_RETURN;
}

int DevFlowManage::handLinkLoop()
{
    time_t cur_t;
    time(&cur_t);
        
    CLockUp lockAuto(&m_lockDevMap);
    if(m_mapDev.size()==0) return 1;
    for(std::map<int,DeviceObj*>::iterator it  = m_mapDev.begin(); it!=m_mapDev.end(); it++)
    {
        DeviceObj *ptrObj = it->second;
        if(NULL == ptrObj) continue;
        
        switch(ptrObj->getStnSta())
        {
            case CONNECT_CLOSE:
                 if (difftime(cur_t, ptrObj->getStnTim()) > m_nMaxTimeOut) // 超时
                 {
                     int nSize = ptrObj->getStnListSize();
                     ptrObj->clearStnMsg();
                     ptrObj->resetStnTim();
                     m_pLogFile->FormatAdd(CLogFile::trace, "DevFlowManage: [stn:%s] [chNo:%d] 设备连接超时，清除站端待发数据缓存,清除个数 %d", m_sStn.c_str(),it->first,nSize);
                 }
                 else
                 { // 发送重连
                     sendConnectToStn(it->first,ptrObj->getProType());
                 }
                 break;
            case CONNECT_CREATE:
            case CONNECT_PROXY:
            default:
                ptrObj->resetStnTim(); // 重置时间
                break;
        }   
    }
    return 1;
}

// nState 0: 未连接，1 连接
void DevFlowManage::nofifyFrontDevState(int nChNo,int nState)
{
      // 回调函数 push 中发送数据
    if(NULL != m_pFrontPtr && NULL != m_pFntDevStaChanFun)
    {
         m_pLogFile->FormatAdd(CLogFile::trace,"DevFlowManage: [stn:%s] [ChNo:%d] nofifyFrontDevState 向前置发送站端设备状态通知, 当前状态: %s"
         ,m_sStn.c_str(),nChNo,(nState==1)? "连接":"断开");
         
        push103 *pPush = (push103*) m_pFrontPtr;
        (pPush->*m_pFntDevStaChanFun)(nChNo, nState);
    }
}

// 向子站发送设备缓存数据
bool DevFlowManage::sendDataToStn(int nChNo,int nPType, std::string &msg)
{
     if(NULL != m_pStnPtr && NULL != m_pSend2StnFun)
     {
         // 回调函数 SendAsdu200Tc103 中发送数据
        CXJPro103ClientWay *p103Way = (CXJPro103ClientWay*) m_pStnPtr;
        (p103Way->*m_pSend2StnFun)(msg, nChNo, nPType);
         return true;
     }
     return false;
}

 // 向前置发送设备缓存数据
bool DevFlowManage::sendDataToFnt(int nChNo, std::string &msg)
{
    // 回调函数 push 中发送数据
    if(NULL!= m_pFrontPtr && NULL !=m_pSend2FrontFun )
    {
        push103 *pPush = (push103*) m_pFrontPtr;
        (pPush->*m_pSend2FrontFun)(msg, nChNo);
         return true;
    }
    return false;
}

bool DevFlowManage::sendConnectToStn(int nChNo,int nPType)
{
    if(NULL!= m_pStnPtr && NULL !=m_pSendConn2StnFun )
    {
         CXJPro103ClientWay *p103Way = (CXJPro103ClientWay*) m_pStnPtr;
         (p103Way->*m_pSendConn2StnFun)( nChNo, nPType);  
          m_pLogFile->FormatAdd(CLogFile::trace, "DevFlowManage: [stn:%s] [chNo:%d] 回调 sendConnectToStn 发送重连", m_sStn.c_str(),nChNo); 
          return false;
    }
    return true;
}

 // 向站端设备发送重连
 bool DevFlowManage::sendCloseConnectToStn(int nChNo,int nPType)
 {
    if(NULL!= m_pStnPtr && NULL !=m_pSendConnClose2StnFun )
    {
         CXJPro103ClientWay *p103Way = (CXJPro103ClientWay*) m_pStnPtr;
         (p103Way->*m_pSendConnClose2StnFun)( nChNo, nPType);  
          m_pLogFile->FormatAdd(CLogFile::trace, "DevFlowManage: [stn:%s] [chNo:%d] 回调 前置设备连接已断开， 向站端设备发送连接关闭通知", m_sStn.c_str(),nChNo); 
          return false;
    }
    return true;
 }

void DevFlowManage::setSendToFrontFun(void * ptr,PW_SEND2FNT_RECALL pFun)
{
    if(NULL != ptr && NULL !=pFun )
    {
        m_pFrontPtr = ptr;
        m_pSend2FrontFun = pFun;
    }
}

void DevFlowManage::setSendToStnFun(void *ptr,PW_SEND2STN_RECALL pFun)
{
    if(NULL != ptr && NULL !=pFun )
    {
        m_pStnPtr = ptr;
        m_pSend2StnFun = pFun;
    }
}

void DevFlowManage::setSendConnectFun(void *ptr,PW_SENDCONNECT_RECALL pFun)
{
    if(NULL != ptr && NULL !=pFun )
    {
        m_pStnPtr = ptr;
        m_pSendConn2StnFun = pFun;
    }
}

void DevFlowManage::setSendConnCloseFun(void * ptr,PW_SENDCONNECTCLOSE_RECALL pFun)
{
    if(NULL != ptr && NULL != pFun)
    {
        m_pStnPtr = ptr;
        m_pSendConnClose2StnFun = pFun;
    }
}

void DevFlowManage::setNotifyDevStateFun(void *ptr,PW_DEVSTATECHANGE_RECALL pFun)
{
    if(NULL != ptr && NULL !=pFun )
    {
        m_pFrontPtr = ptr;
        m_pFntDevStaChanFun = pFun;
    }
}


bool DevFlowManage::startDatHandCenter()
{
    int nRet = xj_thread_create(&m_dealForDatHandle,&m_hDealForDatId,datHandCentThread,this);
    if (nRet != 0) {
        m_pLogFile->FormatAdd(CLogFile::trace, "DevFlowManage:[stn:%s]  创建 datHandCenter 线程失败",m_sStn.c_str());
        return false;
    }
    m_pLogFile->FormatAdd(CLogFile::trace, "DevFlowManage: [stn:%s]  创建 datHandCenter 线程成功",m_sStn.c_str());
    return true;
}

void DevFlowManage::endDatHandCenter()
{
    if (0 != m_hDealForDatId) {
        int nRet = xj_thread_join(m_hDealForDatId, NULL);
        if (nRet != 0) {
            //m_pLogFile->FormatAdd(CLogFile::trace, "DevFlowManage: [stn:%s] endDatHandCenter()退出线程异常",m_sStn.c_str());
            return;
        }
    }
  printf("DevFlowManage: 退出 endDatHandCenter 线程成功");
}

THREAD_FUNC WINAPI DevFlowManage::datHandCentThread( LPVOID pParam)
{
    DevFlowManage* pThis = (DevFlowManage*) pParam;
    pThis->m_pLogFile->FormatAdd(CLogFile::error,"DevFlowManage:  datHandCentThread() 线程ID %d ", syscall(SYS_gettid));
    while (!pThis->isExit()) {
        pThis->loopDatHand();
        MySleep(2);
    }
    printf("DevFlowManage:  退出 datHandCentThread() 线程成功");
    return THREAD_RETURN;
}

int DevFlowManage::loopDatHand()
{
    CLockUp lockAuto(&m_lockDevMap);
    if(m_mapDev.size()==0) return 0;
    for(std::map<int,DeviceObj*>::iterator it  = m_mapDev.begin(); it!=m_mapDev.end(); it++)
    {
        DeviceObj* ptrObj = it->second;
        if(NULL != ptrObj)
        {
            send2StnDeal(ptrObj);
            send2FntDeal(ptrObj);
        }
    }
}
    
// 站端数据向前置发(线程内部)
void DevFlowManage::send2StnDeal(DeviceObj* ptrObj)
{
    std::string sMsg = "";
    int nChNo = ptrObj->getChNo();
    while(ptrObj->getStnMsgEx(sMsg))
    {
        if(ptrObj->getStnSta()!= CONNECT_CLOSE)
        {
            // 回调 向子站发送数据
            sendDataToStn(nChNo,ptrObj->getProType(),sMsg);
            m_pLogFile->FormatAdd(CLogFile::trace,"DevFlowManage: [stn:%s] [ChNo:%d]  向子站发送数据长度 %d",m_sStn.c_str(),nChNo,sMsg.length());
            sMsg.clear();
        }
    }
}

// 前置数据向站端发(线程内部)
void DevFlowManage::send2FntDeal(DeviceObj* ptrObj)
{
    std::string sMsg = "";
    int nChNo = ptrObj->getChNo();
    while(ptrObj->getFntMsgEx(sMsg))
    {
        if(ptrObj->getFntSta())   // 发送前 先判断当前前置中对应设备的连接状态，获取后直接清掉
        {
            sendDataToFnt(nChNo,sMsg);
           // m_pLogFile->FormatAdd(CLogFile::trace,"DevFlowManage: [stn:%s] [ChNo:%d] send2FntDeal 向前置发送数据长度 %d，当前剩余个数 %d",m_sStn.c_str(),nChNo,sMsg.length(),ptrObj->getFntListSize());
        }
        else
        {
            m_pLogFile->FormatAdd(CLogFile::trace,"DevFlowManage: [stn:%s] [ChNo:%d] send2FntDeal 前置设备连接处于断连状态，此数据暂时丢弃!",m_sStn.c_str(),nChNo);
            printBytes(sMsg,nChNo);
            m_pLogFile->FormatAdd(CLogFile::trace,"DevFlowManage: [stn:%s] [ChNo:%d] 数据已清除",m_sStn.c_str(),nChNo);
        }
        sMsg.clear();
    }
}

// 重置站端所有设备的连接状态
 void DevFlowManage::resetStnSta()
 {
     m_pLogFile->FormatAdd(CLogFile::trace,"DevFlowManage: [stn:%s] 子站断联重置所有站端设备状态为关闭",m_sStn.c_str());
     CLockUp lockAuto(&m_lockDevMap);
     for(std::map<int,DeviceObj*>::iterator it  = m_mapDev.begin(); it!=m_mapDev.end(); it++)
     {
         DeviceObj *ptrObj = it->second;
         if(NULL!= ptrObj)
         {
             ptrObj->setStnSta(CONNECT_CLOSE);
             if(ptrObj->getFntSta())nofifyFrontDevState(ptrObj->getChNo(),(ptrObj->getStnSta()==CONNECT_CLOSE) ? 0:1) ;  // 向前置发送设备连接状态通知(前置设备没连上来不发)
         }
     }
 }
 
 void DevFlowManage::printBytes(const string &str,int nChNo)
{
    vector<BYTE> vBytes;
    vBytes.insert(vBytes.end(),str.begin(),str.end());
    string str1;str1.clear();
    for(vector<BYTE>::iterator it=vBytes.begin();it!=vBytes.end();it++){
        char c20[20];
        snprintf(c20,20,"%02x ",(unsigned char)*it);
        str1=str1+c20;
    }
}